/**
 * Insight Model
 * Represents Insight
 */
const { Model, DataTypes } = require('sequelize');
const { InsightStatus } = require('@utils/enums.utils');

class Insight extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        insightText: {
          type: DataTypes.STRING(250),
          allowNull: false,
        },
        sourceUrl: {
          type: DataTypes.STRING,
          allowNull: true,
          validate: {
            isUrl: true,
          },
        },
        pdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
        },
        status: {
          type: DataTypes.ENUM(...InsightStatus.values),
          allowNull: false,
          defaultValue: InsightStatus.PENDING,
        },
        reviewedBy: {
          type: DataTypes.UUID,
          allowNull: true,
          references: {
            model: 'Admin',
            key: 'id',
          },
        },
        reviewedAt: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        rejectionReason: {
          type: DataTypes.STRING,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'Insight',
        tableName: 'Insight',
        timestamps: true,
      }
    );
  }

  static associate(models) {
    // One PD Category
    this.belongsTo(models.PdCategory, {
      foreignKey: 'pdCategoryId',
      as: 'pdCategory',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });

    // One Admin (reviewer)
    this.belongsTo(models.Admin, {
      foreignKey: 'reviewedBy',
      as: 'reviewer',
      onDelete: 'SET NULL',
    });

    // One User (creator)
    this.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'CASCADE',
    });

    // Many Focus Tags
    this.belongsToMany(models.Focus, {
      through: models.InsightFocus,
      as: 'focus',
      foreignKey: 'insightId',
      otherKey: 'focusId',
      onDelete: 'CASCADE',
    });

    // Many WTD Categories
    this.belongsToMany(models.WtdCategory, {
      through: models.InsightWtdCategory,
      as: 'wtdCategories',
      foreignKey: 'insightId',
      otherKey: 'wtdCategoryId',
      onDelete: 'CASCADE',
    });

    // Bookmarked by users association
    this.belongsToMany(models.User, {
      through: models.BookmarkedInsight,
      as: 'bookmarkedByUsers',
      foreignKey: 'insightId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Liked by users association
    this.belongsToMany(models.User, {
      through: models.LikedInsight,
      as: 'likedByUsers',
      foreignKey: 'insightId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Implemented by users association
    this.belongsToMany(models.User, {
      through: models.ImplementedInsight,
      as: 'implementedByUsers',
      foreignKey: 'insightId',
      otherKey: 'userId',
      onDelete: 'CASCADE',
    });

    // Has many contributions
    this.hasMany(models.Contribution, {
      foreignKey: 'insightId',
      as: 'contributions',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = Insight;
