/**
 * ExperienceWeekInsight Model
 * Represents insights within experience weeks (max 5 per week)
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekInsight extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        experienceWeekId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeek',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 5,
          },
          comment: 'Order of insight within week (1-5)',
        },
        text: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
            len: [1, 500], // Reasonable limit for insight text
          },
        },
        sourceUrl: {
          type: DataTypes.TEXT,
          allowNull: true,
          validate: {
            isUrl: {
              msg: 'Source URL must be a valid URL',
            },
          },
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekInsight',
        tableName: 'ExperienceWeekInsight',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceWeekId'],
            name: 'experience_week_insight_week_id_idx',
          },
          {
            fields: ['order'],
            name: 'experience_week_insight_order_idx',
          },
          {
            unique: true,
            fields: ['experienceWeekId', 'order'],
            name: 'experience_week_insight_week_order_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to ExperienceWeek
    this.belongsTo(models.ExperienceWeek, {
      foreignKey: 'experienceWeekId',
      as: 'experienceWeek',
      onDelete: 'CASCADE',
    });

    // Many-to-many with Focus through ExperienceWeekInsightFocus
    this.belongsToMany(models.Focus, {
      through: models.ExperienceWeekInsightFocus,
      as: 'focuses',
      foreignKey: 'experienceWeekInsightId',
      otherKey: 'focusId',
      onDelete: 'CASCADE',
    });

    // Many-to-many with WtdCategory through ExperienceWeekInsightWtdCategory
    this.belongsToMany(models.WtdCategory, {
      through: models.ExperienceWeekInsightWtdCategory,
      as: 'wtdCategories',
      foreignKey: 'experienceWeekInsightId',
      otherKey: 'wtdCategoryId',
      onDelete: 'CASCADE',
    });

    // Many-to-many with PdCategory through ExperienceWeekInsightPdCategory
    this.belongsToMany(models.PdCategory, {
      through: models.ExperienceWeekInsightPdCategory,
      as: 'pdCategories',
      foreignKey: 'experienceWeekInsightId',
      otherKey: 'pdCategoryId',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields if needed
    return values;
  }
}

module.exports = ExperienceWeekInsight;
