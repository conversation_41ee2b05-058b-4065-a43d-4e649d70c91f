/**
 * Experience Enrollment Repository
 *
 * This repository handles experience enrollment-related database operations
 */
const databaseService = require('@config/database.config');
const commonRepository = require('./common.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');

/**
 * Experience enrollment repository methods
 */
class ExperienceEnrollmentRepository {
  constructor() {
    this.models = {
      ExperienceEnrollment: databaseService.getExperienceEnrollmentModel(),
      Experience: databaseService.getExperienceModel(),
    };
  }

  /**
   * Find enrollment by ID
   * @param {string} id - Enrollment ID
   * @returns {Promise<Object>} Enrollment object
   */
  async findById(id) {
    return this.models.ExperienceEnrollment.findByPk(id);
  }

  /**
   * Find enrollment by experience and user
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Enrollment object
   */
  async findByExperienceAndUser(experienceId, userId) {
    return this.models.ExperienceEnrollment.findOne({
      where: { experienceId, userId },
    });
  }

  /**
   * Find enrollments by user with pagination
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Paginated enrollments
   */
  async findByUser(userId, options) {
    const { page = 1, limit = 10 } = options;
    const offset = commonRepository.calculateOffset(page, limit);

    const { count, rows } =
      await this.models.ExperienceEnrollment.findAndCountAll({
        where: { userId },
        include: [
          {
            model: this.models.Experience,
            attributes: ['id', 'title', 'description', 'duration'],
          },
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset,
      });

    return {
      data: rows,
      pagination: commonRepository.buildPaginationInfo(count, page, limit),
    };
  }

  /**
   * Create enrollment
   * @param {Object} data - Enrollment data
   * @returns {Promise<Object>} Created enrollment
   */
  async create(data) {
    try {
      return await this.models.ExperienceEnrollment.create(data);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw new ApiException(
          HttpStatus.CONFLICT,
          EXPERIENCE_ENROLLMENT.ALREADY_ENROLLED
        );
      }
      throw error;
    }
  }

  /**
   * Update enrollment
   * @param {string} id - Enrollment ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated enrollment
   */
  async update(id, data) {
    const enrollment = await this.findById(id);
    if (!enrollment) {
      throw new ApiException(
        HttpStatus.NOT_FOUND,
        EXPERIENCE_ENROLLMENT.NOT_FOUND
      );
    }

    return enrollment.update(data);
  }
}

module.exports = new ExperienceEnrollmentRepository();
