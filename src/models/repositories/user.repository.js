/**
 * User Repository
 *
 * Handles data access operations for the User model
 */
const { ApiException } = require('@utils/exception.utils');
const User = require('@models/user.model');
const { USER, AUTH } = require('@utils/messages.utils');
const { Op } = require('sequelize');

class UserRepository {
  /**
   * Find user by ID
   * @param {string} id - User UUID
   * @returns {Promise<User>} User instance
   * @throws {ApiException} If user not found
   */
  async findById(id) {
    try {
      const user = await User.findByPk(id);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      return user;
    } catch (error) {
      // Log the error for debugging
      console.error('Error in findById repository:', error);

      // Simply rethrow the error
      throw error;
    }
  }

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Promise<User|null>} User instance or null
   */
  async findByEmail(email) {
    try {
      return await User.findOne({ where: { email: email.toLowerCase() } });
    } catch (error) {
      console.error('Error in findByEmail repository:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<User>} Created user
   * @throws {ApiException} If email already exists
   */
  async create(userData) {
    try {
      const existingUser = await this.findByEmail(userData.email);

      if (existingUser) {
        throw new ApiException(409, AUTH.EMAIL_ALREADY_EXISTS);
      }

      return await User.create(userData);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {string} id - User UUID
   * @param {Object} updateData - Data to update
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async update(id, updateData) {
    try {
      const user = await this.findById(id);
      await user.update(updateData);
      return user;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete user
   * @param {string} id - User UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If user not found
   */
  async delete(id) {
    try {
      const user = await this.findById(id);
      await user.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Change user password
   * @param {string} id - User UUID
   * @param {string} newPassword - New password
   * @returns {Promise<User>} Updated user
   * @throws {ApiException} If user not found
   */
  async changePassword(id, newPassword) {
    try {
      const user = await User.findByPk(id);

      if (!user) {
        throw new ApiException(404, USER.USER_NOT_FOUND);
      }

      user.password = newPassword;
      await user.save();
      return user;
    } catch (error) {
      console.error('Error in changePassword repository:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new UserRepository();
