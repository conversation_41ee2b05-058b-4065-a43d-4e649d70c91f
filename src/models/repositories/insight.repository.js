/**
 * Insight Repository
 *
 * Handles data access operations for the Insight model
 */
const { ApiException } = require('@utils/exception.utils');
const { InsightStatus } = require('@utils/enums.utils');
const { PAGINATION, PD_SPOTLIGHT, TRENDING } = require('@utils/constants');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;
const commonRepository = require('./common.repository');
const { INSIGHT } = require('@utils/messages.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Insight: databaseService.getInsightModel(),
      User: databaseService.getUserModel(),
      PdCategory: databaseService.getPdCategoryModel(),
      Focus: databaseService.getFocusModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      Admin: databaseService.getAdminModel(),
      BookmarkedInsight: databaseService.getBookmarkedInsightModel(),
      LikedInsight: databaseService.getLikedInsightModel(),
      ImplementedInsight: databaseService.getImplementedInsightModel(),
      InsightFocus: databaseService.getInsightFocusModel(),
      InsightWtdCategory: databaseService.getInsightWtdCategoryModel(),
    };
  }

  _getInsightAttributes() {
    return {
      exclude: [
        'pdCategoryId',
        'createdBy',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
            ),
          'likesCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
            ),
          'implementationCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id")`
            ),
          'totalContributions',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
            ),
          'bookmarksCount',
        ],
      ],
    };
  }

  _getInsightIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'profilePic'],
      },
      {
        model: this.models.PdCategory,
        as: 'pdCategory',
        attributes: ['id', 'name'],
      },
      {
        model: this.models.Focus,
        as: 'focus',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ];
  }

  _getAdminInsightIncludes() {
    return [
      ...this._getInsightIncludes(),
      {
        model: this.models.Admin,
        as: 'reviewer',
        attributes: ['id', 'email'],
      },
    ];
  }
}

/**
 * Repository for complex search and filtering operations
 */
class InsightSearchRepository extends BaseRepository {
  async buildWhereClause({ search, focusIds, pdCategoryIds, wtdCategoryIds }) {
    const whereClause = {
      status: InsightStatus.APPROVED,
    };

    if (search) {
      whereClause.insightText = {
        [Op.iLike]: `%${search}%`,
      };
    }

    if (pdCategoryIds?.length > 0) {
      const insightsWithPdCategory = await this.models.Insight.findAll({
        attributes: ['id'],
        where: {
          pdCategoryId: {
            [Op.in]: pdCategoryIds,
          },
          status: InsightStatus.APPROVED,
        },
        raw: true,
      });

      const insightIds = insightsWithPdCategory.map((item) => item.id);
      if (insightIds.length > 0) {
        whereClause.id = {
          [Op.in]: insightIds,
        };
      } else {
        whereClause.id = null;
      }
    }

    if (focusIds?.length > 0) {
      const insightIdsWithFocus = await this.models.InsightFocus.findAll({
        attributes: ['insightId'],
        where: {
          focusId: {
            [Op.in]: focusIds,
          },
        },
        raw: true,
      });

      const insightIds = insightIdsWithFocus.map((item) => item.insightId);
      if (insightIds.length > 0) {
        if (whereClause.id) {
          const existingIds = whereClause.id[Op.in];
          const intersectionIds = existingIds.filter((id) =>
            insightIds.includes(id)
          );
          whereClause.id = {
            [Op.in]: intersectionIds.length > 0 ? intersectionIds : [null],
          };
        } else {
          whereClause.id = {
            [Op.in]: insightIds,
          };
        }
      } else {
        whereClause.id = null;
      }
    }

    if (wtdCategoryIds?.length > 0) {
      const insightIdsWithWtdCategory =
        await this.models.InsightWtdCategory.findAll({
          attributes: ['insightId'],
          where: {
            wtdCategoryId: {
              [Op.in]: wtdCategoryIds,
            },
          },
          raw: true,
        });

      const insightIds = insightIdsWithWtdCategory.map(
        (item) => item.insightId
      );
      if (insightIds.length > 0) {
        if (whereClause.id) {
          const existingIds = whereClause.id[Op.in];
          const intersectionIds = existingIds.filter((id) =>
            insightIds.includes(id)
          );
          whereClause.id = {
            [Op.in]: intersectionIds.length > 0 ? intersectionIds : [null],
          };
        } else {
          whereClause.id = {
            [Op.in]: insightIds,
          };
        }
      } else if (wtdCategoryIds.length > 0) {
        whereClause.id = null;
      }
    }

    return whereClause;
  }
}

/**
 * Helper class for Insight operations
 */
class InsightHelper extends BaseRepository {
  async enrichInsightsWithUserData(insights, userId) {
    if (!userId) return insights;

    const insightIds = insights.map((insight) => insight.id);
    const [bookmarks, likes, implementations] = await Promise.all([
      this.models.BookmarkedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.LikedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.ImplementedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
    ]);

    const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
    const likedInsightIds = new Set(likes.map((l) => l.insightId));
    const implementedInsightIds = new Set(
      implementations.map((i) => i.insightId)
    );

    return insights.map((insight) => {
      const insightData = insight.toJSON();
      insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
      insightData.isLiked = likedInsightIds.has(insight.id);
      insightData.isImplemented = implementedInsightIds.has(insight.id);
      return insightData;
    });
  }

  async enrichInsightWithUserData(insight, userId) {
    const [bookmark, like, implementation] = await Promise.all([
      this.models.BookmarkedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
      this.models.LikedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
      this.models.ImplementedInsight.findOne({
        where: {
          userId,
          insightId: insight.id,
        },
      }),
    ]);

    const insightData = insight.toJSON();
    insightData.isBookmarked = !!bookmark;
    insightData.isLiked = !!like;
    insightData.isImplemented = !!implementation;
    return insightData;
  }

  async createFocusAssociations(insightId, focusIds, transaction) {
    const focusEntries = focusIds.map((focusId) => ({
      id: uuidv4(),
      insightId,
      focusId,
    }));
    await this.models.InsightFocus.bulkCreate(focusEntries, { transaction });
  }

  async createWtdCategoryAssociations(insightId, wtdCategoryIds, transaction) {
    const wtdCategoryEntries = wtdCategoryIds.map((wtdCategoryId) => ({
      id: uuidv4(),
      insightId,
      wtdCategoryId,
    }));
    await this.models.InsightWtdCategory.bulkCreate(wtdCategoryEntries, {
      transaction,
    });
  }

  async updateFocusAssociations(insightId, focusIds, transaction) {
    await this.models.InsightFocus.destroy({
      where: { insightId },
      transaction,
    });

    if (focusIds.length > 0) {
      await this.createFocusAssociations(insightId, focusIds, transaction);
    }
  }

  async updateWtdCategoryAssociations(insightId, wtdCategoryIds, transaction) {
    await this.models.InsightWtdCategory.destroy({
      where: { insightId },
      transaction,
    });

    if (wtdCategoryIds.length > 0) {
      await this.createWtdCategoryAssociations(
        insightId,
        wtdCategoryIds,
        transaction
      );
    }
  }

  async findPdCategoryByName(pdCategoryName) {
    const PdCategory = databaseService.getPdCategoryModel();
    return await PdCategory.findOne({
      where: { name: pdCategoryName },
      raw: true,
    });
  }

  getPDSpotlightIncludes() {
    const User = databaseService.getUserModel();
    const PdCategory = databaseService.getPdCategoryModel();
    const Focus = databaseService.getFocusModel();
    const WtdCategory = databaseService.getWtdCategoryModel();

    return [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'profilePic'],
      },
      {
        model: PdCategory,
        as: 'pdCategory',
        attributes: ['id', 'name'],
      },
      {
        model: Focus,
        as: 'focus',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
      {
        model: WtdCategory,
        as: 'wtdCategories',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ];
  }

  getPDSpotlightAttributes() {
    return {
      exclude: [
        'pdCategoryId',
        'createdBy',
        'status',
        'reviewedBy',
        'reviewedAt',
        'rejectionReason',
      ],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id")`
            ),
          'likesCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "ImplementedInsight" WHERE "ImplementedInsight"."insightId" = "Insight"."id")`
            ),
          'implementationCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id")`
            ),
          'totalContributions',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "BookmarkedInsight" WHERE "BookmarkedInsight"."insightId" = "Insight"."id")`
            ),
          'bookmarksCount',
        ],
      ],
    };
  }

  async findPDSpotlightInsights(pdCategoryId, page, limit) {
    const Insight = databaseService.getInsightModel();
    const offset = (page - 1) * limit;

    return await Insight.findAndCountAll({
      where: {
        status: InsightStatus.APPROVED,
        pdCategoryId: pdCategoryId,
      },
      limit,
      offset,
      distinct: true,
      order: [['createdAt', 'DESC']],
      attributes: this.getPDSpotlightAttributes(),
      include: this.getPDSpotlightIncludes(),
      subQuery: false,
    });
  }

  async enrichPDSpotlightInsights(insights, userId) {
    if (!userId) return insights;

    const insightIds = insights.map((insight) => insight.id);
    const [bookmarks, implementations] = await Promise.all([
      this.models.BookmarkedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
      this.models.ImplementedInsight.findAll({
        where: {
          userId,
          insightId: { [Op.in]: insightIds },
        },
        raw: true,
      }),
    ]);

    const bookmarkedInsightIds = new Set(bookmarks.map((b) => b.insightId));
    const implementedInsightIds = new Set(
      implementations.map((i) => i.insightId)
    );

    return insights.map((insight) => {
      const insightData = insight.toJSON ? insight.toJSON() : insight;
      insightData.isBookmarked = bookmarkedInsightIds.has(insight.id);
      insightData.isLiked = false; // Not using likes data in PD Spotlight
      insightData.isImplemented = implementedInsightIds.has(insight.id);
      return insightData;
    });
  }
}

/**
 * Main Insight Repository class
 */
class InsightRepository extends BaseRepository {
  constructor() {
    super();
    this.searchRepo = new InsightSearchRepository();
    this.helper = new InsightHelper();
  }

  /**
   * Create a new insight
   * @param {Object} data - Insight data
   * @param {string} data.insightText - The insight text
   * @param {string} data.sourceUrl - Optional source URL
   * @param {string} data.pdCategoryId - PD category ID
   * @param {string} data.createdBy - User ID of creator
   * @param {Array<string>} data.focusIds - Array of focus IDs
   * @param {Array<string>} data.wtdCategoryIds - Array of WTD category IDs
   * @returns {Promise<Insight>} Created insight
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const insight = await this.models.Insight.create(
        {
          insightText: data.insightText,
          sourceUrl: data.sourceUrl,
          pdCategoryId: data.pdCategoryId,
          createdBy: data.createdBy,
          status: data.status || 'PENDING',
        },
        { transaction }
      );

      if (data.focusIds?.length > 0) {
        await this.helper.createFocusAssociations(
          insight.id,
          data.focusIds,
          transaction
        );
      }

      if (data.wtdCategoryIds?.length > 0) {
        await this.helper.createWtdCategoryAssociations(
          insight.id,
          data.wtdCategoryIds,
          transaction
        );
      }

      await transaction.commit();
      return await this.findById(insight.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Find all insights with pagination and optional search (for regular users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {Array<string>} options.focusIds - Optional array of focus IDs to filter by
   * @param {Array<string>} options.pdCategoryIds - Optional array of PD category IDs to filter by
   * @param {Array<string>} options.wtdCategoryIds - Optional array of WTD category IDs to filter by
   * @param {string} options.userId - Optional user ID to check if insights are bookmarked
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAll({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    search = '',
    focusIds = [],
    pdCategoryIds = [],
    wtdCategoryIds = [],
    userId = null,
  } = {}) {
    try {
      const whereClause = await this.searchRepo.buildWhereClause({
        search,
        focusIds,
        pdCategoryIds,
        wtdCategoryIds,
      });

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        distinct: true,
        order: [['createdAt', 'DESC']],
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        subQuery: false,
      });

      const insights = await this.helper.enrichInsightsWithUserData(
        rows,
        userId
      );

      return {
        insights,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find insight by ID (for regular users)
   * @param {string} id - Insight UUID
   * @param {string} userId - Optional user ID to check if insight is bookmarked
   * @returns {Promise<Insight>} Insight instance
   * @throws {ApiException} If insight not found
   */
  async findById(id, userId = null) {
    try {
      const insight = await this.models.Insight.findByPk(id, {
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        where: {
          status: InsightStatus.APPROVED,
        },
      });

      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      if (userId) {
        return await this.helper.enrichInsightWithUserData(insight, userId);
      }

      return insight;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find all insights with pagination and optional search (for admin users)
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for insightText
   * @param {string} options.status - Optional filter by status
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findAllForAdmin({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    search = '',
    status = null,
  } = {}) {
    try {
      const whereClause = {};
      if (search) {
        whereClause.insightText = {
          [Op.iLike]: `%${search}%`,
        };
      }
      if (status) {
        whereClause.status = status;
      }

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        attributes: {
          exclude: ['pdCategoryId', 'createdBy', 'reviewedBy', 'reviewedAt'],
        },
        include: this._getAdminInsightIncludes(),
        distinct: true,
      });

      return {
        insights: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findAllForAdmin repository:', error);
      throw error;
    }
  }

  /**
   * Find insight by ID (for admin users)
   * @param {string} id - Insight UUID
   * @returns {Promise<Insight>} Insight instance with admin-specific data
   * @throws {ApiException} If insight not found
   */
  async findByIdForAdmin(id) {
    try {
      const insight = await this.models.Insight.findByPk(id, {
        attributes: {
          exclude: ['pdCategoryId', 'createdBy', 'reviewedBy', 'reviewedAt'],
        },
        include: this._getAdminInsightIncludes(),
      });

      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      return insight;
    } catch (error) {
      console.error('Error in findByIdForAdmin repository:', error);
      throw error;
    }
  }

  /**
   * Update insight
   * @param {string} id - Insight UUID
   * @param {Object} data - Data to update
   * @param {string} [data.insightText] - Updated insight text
   * @param {string} [data.sourceUrl] - Updated source URL
   * @param {string} [data.pdCategoryId] - Updated PD category ID
   * @param {Array<string>} [data.focusIds] - Updated array of focus IDs
   * @param {Array<string>} [data.wtdCategoryIds] - Updated array of WTD category IDs
   * @returns {Promise<Insight>} Updated insight
   * @throws {ApiException} If insight not found
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const insight = await this.findById(id);
      Object.assign(insight, data);
      await insight.save({ transaction });

      if (data.focusIds) {
        await this.helper.updateFocusAssociations(
          insight.id,
          data.focusIds,
          transaction
        );
      }

      if (data.wtdCategoryIds) {
        await this.helper.updateWtdCategoryAssociations(
          insight.id,
          data.wtdCategoryIds,
          transaction
        );
      }

      await transaction.commit();
      return await this.findById(insight.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete insight
   * @param {string} id - Insight UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If insight not found
   */
  async delete(id) {
    try {
      const insight = await this.findById(id);
      await insight.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }

  /**
   * Find trending insights based on contributions and likes
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.userId - User ID to check if insights are bookmarked/liked/implemented
   * @returns {Promise<Object>} Object containing trending insights and pagination info
   */
  async findTrending({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    userId,
  } = {}) {
    try {
      const whereClause = {
        status: InsightStatus.APPROVED,
      };

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Insight.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        distinct: true,
        order: [
          [
            databaseService.getSequelize().literal(`
              (
                (SELECT COUNT(*)::INTEGER FROM "LikedInsight" WHERE "LikedInsight"."insightId" = "Insight"."id") * ${TRENDING.WEIGHTS.LIKES} +
                (SELECT COUNT(*)::INTEGER FROM "Contribution" WHERE "Contribution"."insightId" = "Insight"."id") * ${TRENDING.WEIGHTS.CONTRIBUTIONS}
              )
            `),
            'DESC',
          ],
          ['createdAt', 'DESC'],
        ],
        attributes: this._getInsightAttributes(),
        include: this._getInsightIncludes(),
        subQuery: false,
      });

      const insights = await this.helper.enrichInsightsWithUserData(
        rows,
        userId
      );

      return {
        insights,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findTrending repository:', error);
      throw error;
    }
  }

  /**
   * Find insights by PD category name (specifically for PD Spotlight)
   * @param {Object} options - Query options
   * @param {string} options.pdCategoryName - PD category name to filter by
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.userId - User ID to check if insights are bookmarked/liked/implemented
   * @returns {Promise<Object>} Object containing insights and pagination info
   */
  async findPDSpotlight({
    pdCategoryName = PD_SPOTLIGHT.DEFAULT_CATEGORY,
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    userId,
  } = {}) {
    try {
      // Find the PD category
      const pdCategory = await this.helper.findPdCategoryByName(pdCategoryName);

      if (!pdCategory) {
        return {
          insights: [],
          pagination: commonRepository.buildPaginationInfo(0, page, limit),
        };
      }

      // Find insights with pagination
      const { count, rows } = await this.helper.findPDSpotlightInsights(
        pdCategory.id,
        page,
        limit
      );

      // Enrich insights with user data if userId is provided
      const insights = userId
        ? await this.helper.enrichPDSpotlightInsights(rows, userId)
        : rows;

      return {
        insights,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findPDSpotlight repository:', error);
      throw error;
    }
  }
}

module.exports = new InsightRepository();
