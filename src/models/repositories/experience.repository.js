/**
 * Experience Repository
 * Handles database operations for Experience model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { v4: uuidv4 } = require('uuid');
const commonRepository = require('./common.repository');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Experience: databaseService.getExperienceModel(),
      ExperienceMedia: databaseService.getExperienceMediaModel(),
      ExperiencePdCategory: databaseService.getExperiencePdCategoryModel(),
      ExperienceWtdCategory: databaseService.getExperienceWtdCategoryModel(),
      ExperienceWeek: databaseService.getExperienceWeekModel(),
      ExperienceWeekMedia: databaseService.getExperienceWeekMediaModel(),
      ExperienceWeekInsight: databaseService.getExperienceWeekInsightModel(),
      ExperienceWeekInsightFocus:
        databaseService.getExperienceWeekInsightFocusModel(),
      ExperienceWeekInsightPdCategory:
        databaseService.getExperienceWeekInsightPdCategoryModel(),
      ExperienceWeekInsightWtdCategory:
        databaseService.getExperienceWeekInsightWtdCategoryModel(),
      User: databaseService.getUserModel(),
      PdCategory: databaseService.getPdCategoryModel(),
      WtdCategory: databaseService.getWtdCategoryModel(),
      Focus: databaseService.getFocusModel(),
    };
  }

  _getExperienceAttributes() {
    return {
      exclude: ['createdBy'],
      include: [
        [databaseService.getSequelize().literal('0'), 'ratings'],
        [databaseService.getSequelize().literal('false'), 'isEnrolled'],
      ],
    };
  }

  _getExperienceIncludes() {
    return [
      {
        model: this.models.User,
        as: 'creator',
        attributes: ['id', 'firstName', 'lastName', 'email', 'profilePic'],
      },
      {
        model: this.models.ExperienceMedia,
        as: 'media',
        required: false,
        attributes: ['id', 'type', 'url', 'title', 'order'],
      },
      {
        model: this.models.PdCategory,
        as: 'pdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
      {
        model: this.models.WtdCategory,
        as: 'wtdCategories',
        through: { attributes: [] },
        required: false,
        attributes: ['id', 'name'],
      },
    ];
  }
}

/**
 * Experience Helper class for common operations
 */
class ExperienceHelper {
  constructor(models) {
    this.models = models;
  }

  async createMedia(experienceId, media, transaction) {
    if (!media.length) return;

    const mediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceMedia.bulkCreate(mediaData, { transaction });
  }

  async createCategories(
    experienceId,
    pdCategoryIds,
    wtdCategoryIds,
    transaction
  ) {
    if (pdCategoryIds.length) {
      // Validate PD Category IDs
      const validPdCategories = await this.models.PdCategory.findAll({
        where: { id: pdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validPdIds = validPdCategories.map((cat) => cat.id);
      const invalidPdIds = pdCategoryIds.filter(
        (id) => !validPdIds.includes(id)
      );

      if (invalidPdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_PD_CATEGORY_IDS.replace(
            '%s',
            invalidPdIds.join(', ')
          )
        );
      }

      const pdCategoryData = validPdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        pdCategoryId: id,
      }));
      await this.models.ExperiencePdCategory.bulkCreate(pdCategoryData, {
        transaction,
      });
    }

    if (wtdCategoryIds.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_WTD_CATEGORY_IDS.replace(
            '%s',
            invalidWtdIds.join(', ')
          )
        );
      }

      const wtdCategoryData = validWtdIds.map((id) => ({
        id: uuidv4(),
        experienceId,
        wtdCategoryId: id,
      }));
      await this.models.ExperienceWtdCategory.bulkCreate(wtdCategoryData, {
        transaction,
      });
    }
  }

  async createWeeks(experienceId, weeks, transaction) {
    for (const week of weeks) {
      const experienceWeek = await this.models.ExperienceWeek.create(
        {
          id: uuidv4(),
          experienceId,
          weekNumber: week.weekNumber,
          title: week.title,
          weeklyWhy: week.weeklyWhy,
        },
        { transaction }
      );

      await this.createWeekMedia(experienceWeek.id, week.media, transaction);
      await this.createInsights(experienceWeek.id, week.insights, transaction);
    }
  }

  async createWeekMedia(weekId, media, transaction) {
    if (!media?.length) return;

    const weekMediaData = media.map((item, index) => ({
      id: uuidv4(),
      experienceWeekId: weekId,
      type: item.type,
      url: item.url,
      title: item.title,
      order: index + 1,
    }));

    await this.models.ExperienceWeekMedia.bulkCreate(weekMediaData, {
      transaction,
    });
  }

  async createInsights(weekId, insights, transaction) {
    if (!insights?.length) return;

    for (let i = 0; i < insights.length; i++) {
      const data = insights[i];

      const insight = await this.models.ExperienceWeekInsight.create(
        {
          id: uuidv4(),
          experienceWeekId: weekId,
          order: i + 1,
          text: data.text,
          sourceUrl: data.sourceUrl,
        },
        { transaction }
      );

      await this.createInsightAssociations(insight.id, data, transaction);
    }
  }

  async createInsightAssociations(insightId, data, transaction) {
    if (data.focusIds?.length) {
      // Validate Focus IDs
      const validFocuses = await this.models.Focus.findAll({
        where: { id: data.focusIds },
        attributes: ['id'],
        transaction,
      });

      const validFocusIds = validFocuses.map((focus) => focus.id);
      const invalidFocusIds = data.focusIds.filter(
        (id) => !validFocusIds.includes(id)
      );

      if (invalidFocusIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_FOCUS_IDS.replace('%s', invalidFocusIds.join(', '))
        );
      }

      const focusData = validFocusIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        focusId: id,
      }));
      await this.models.ExperienceWeekInsightFocus.bulkCreate(focusData, {
        transaction,
      });
    }

    if (data.pdCategoryIds?.length) {
      // Validate PD Category IDs
      const validPdCategories = await this.models.PdCategory.findAll({
        where: { id: data.pdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validPdIds = validPdCategories.map((cat) => cat.id);
      const invalidPdIds = data.pdCategoryIds.filter(
        (id) => !validPdIds.includes(id)
      );

      if (invalidPdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_PD_CATEGORY_IDS_INSIGHT.replace(
            '%s',
            invalidPdIds.join(', ')
          )
        );
      }

      const pdData = validPdIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        pdCategoryId: id,
      }));
      await this.models.ExperienceWeekInsightPdCategory.bulkCreate(pdData, {
        transaction,
      });
    }

    if (data.wtdCategoryIds?.length) {
      // Validate WTD Category IDs
      const validWtdCategories = await this.models.WtdCategory.findAll({
        where: { id: data.wtdCategoryIds },
        attributes: ['id'],
        transaction,
      });

      const validWtdIds = validWtdCategories.map((cat) => cat.id);
      const invalidWtdIds = data.wtdCategoryIds.filter(
        (id) => !validWtdIds.includes(id)
      );

      if (invalidWtdIds.length > 0) {
        throw new ApiException(
          HttpStatus.BAD_REQUEST,
          EXPERIENCE.INVALID_WTD_CATEGORY_IDS_INSIGHT.replace(
            '%s',
            invalidWtdIds.join(', ')
          )
        );
      }

      const wtdData = validWtdIds.map((id) => ({
        id: uuidv4(),
        experienceWeekInsightId: insightId,
        wtdCategoryId: id,
      }));
      await this.models.ExperienceWeekInsightWtdCategory.bulkCreate(wtdData, {
        transaction,
      });
    }
  }
}

/**
 * Main Experience Repository class
 */
class ExperienceRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new ExperienceHelper(this.models);
  }

  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();
    try {
      const experience = await this.models.Experience.create(
        {
          title: data.title,
          shortDescription: data.shortDescription,
          longDescription: data.longDescription,
          experienceLength: data.experienceLength,
          personalNote: data.personalNote,
          createdBy: data.createdBy,
        },
        { transaction }
      );

      await this.helper.createMedia(
        experience.id,
        data.media || [],
        transaction
      );
      await this.helper.createCategories(
        experience.id,
        data.pdCategoryIds || [],
        data.wtdCategoryIds || [],
        transaction
      );
      await this.helper.createWeeks(
        experience.id,
        data.weeks || [],
        transaction
      );

      await transaction.commit();
      return await this.findById(experience.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async findAll({ page = 1, limit = 10, createdBy = '' } = {}) {
    try {
      const whereClause = {};

      if (createdBy) whereClause.createdBy = createdBy;

      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } = await this.models.Experience.findAndCountAll({
        where: whereClause,
        attributes: this._getExperienceAttributes(),
        include: this._getExperienceIncludes(),
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        distinct: true,
      });

      return {
        experiences: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      throw error;
    }
  }

  async findById(id, includeWeeks = true) {
    try {
      const includeOptions = [...this._getExperienceIncludes()];

      if (includeWeeks) {
        includeOptions.push({
          model: this.models.ExperienceWeek,
          as: 'weeks',
          required: false,
          include: [
            {
              model: this.models.ExperienceWeekMedia,
              as: 'media',
              required: false,
              attributes: ['id', 'type', 'url', 'title', 'order'],
            },
            {
              model: this.models.ExperienceWeekInsight,
              as: 'insights',
              required: false,
              include: [
                {
                  model: this.models.Focus,
                  as: 'focuses',
                  through: { attributes: [] },
                  attributes: ['id', 'name'],
                },
                {
                  model: this.models.PdCategory,
                  as: 'pdCategories',
                  through: { attributes: [] },
                  attributes: ['id', 'name'],
                },
                {
                  model: this.models.WtdCategory,
                  as: 'wtdCategories',
                  through: { attributes: [] },
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        });
      }

      const experience = await this.models.Experience.findByPk(id, {
        attributes: this._getExperienceAttributes(),
        include: includeOptions,
      });

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ExperienceRepository();
