/**
 * Focus Model
 * Represents Focus
 */
const { Model, DataTypes } = require('sequelize');

class Focus extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
          // Explicitly preserve the original case
          set(value) {
            this.setDataValue('name', value);
          },
        },
      },
      {
        sequelize,
        modelName: 'Focus',
        tableName: 'Focus',
        timestamps: true,
        indexes: [
          // Create a unique index on the lowercase version of the name
          {
            unique: true,
            fields: [sequelize.fn('lower', sequelize.col('name'))],
            name: 'focus_name_lower_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    this.belongsToMany(models.Insight, {
      through: models.InsightFocus,
      as: 'insights',
      foreignKey: 'focusId',
      otherKey: 'insightId',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = Focus;
