/**
 * Experience Enrollment Service
 *
 * Handles business logic for experience enrollments
 */
const experienceEnrollmentRepository = require('@models/repositories/experience-enrollment.repository');
const experienceRepository = require('@models/repositories/experience.repository');
const { ApiException } = require('@utils/exception.utils');
const { EXPERIENCE_ENROLLMENT } = require('@utils/messages.utils');

/**
 * Experience Enrollment Service
 */
const experienceEnrollmentService = {
  /**
   * Enroll a user in an experience
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date for the experience
   * @returns {Promise<Object>} Created enrollment
   */
  enrollUser: async (experienceId, userId, startDate) => {
    try {
      // Check if experience exists
      const experience = await experienceRepository.findById(experienceId);
      if (!experience) {
        throw new ApiException(404, EXPERIENCE_ENROLLMENT.EXPERIENCE_NOT_FOUND);
      }

      // Check if user is already enrolled
      const existingEnrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );
      if (existingEnrollment) {
        throw new ApiException(400, EXPERIENCE_ENROLLMENT.ALREADY_ENROLLED);
      }

      // Create enrollment
      const enrollment = await experienceEnrollmentRepository.create({
        experienceId,
        userId,
        startDate,
        status: 'REGISTERED',
      });

      return enrollment;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all enrollments for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Enrollments and pagination info
   */
  getUserEnrollments: async (userId, { page, limit }) => {
    try {
      return await experienceEnrollmentRepository.findByUser(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update enrollment status
   * @param {string} experienceId - Experience ID
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated enrollment
   */
  updateEnrollmentStatus: async (experienceId, userId, status) => {
    try {
      // Check if enrollment exists and belongs to user
      const enrollment =
        await experienceEnrollmentRepository.findByExperienceAndUser(
          experienceId,
          userId
        );

      if (!enrollment) {
        throw new ApiException(404, EXPERIENCE_ENROLLMENT.NOT_FOUND);
      }

      // Update status
      const updatedEnrollment = await experienceEnrollmentRepository.update(
        enrollment.id,
        { status }
      );

      return updatedEnrollment;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceEnrollmentService;
