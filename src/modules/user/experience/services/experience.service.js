/**
 * Experience Service
 *
 * Business logic for experience operations
 */
const experienceRepository = require('@models/repositories/experience.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');

/**
 * Experience service
 */
const experienceService = {
  /**
   * Create a new experience with weeks, insights, and media
   * @param {Object} data - Experience data
   * @param {string} userId - Creator user ID
   * @returns {Promise<Object>} Created experience
   */
  createExperience: async (data, userId) => {
    try {
      const experienceData = {
        ...data,
        createdBy: userId,
      };

      return await experienceRepository.create(experienceData);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all experiences with pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Experiences with pagination
   */
  getAllExperiences: async (options) => {
    try {
      const { page = 1, limit = 10, createdBy } = options;

      return await experienceRepository.findAll({
        page,
        limit,
        createdBy,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get experience by ID with all associations
   * @param {string} id - Experience ID
   * @returns {Promise<Object>} Experience with associations
   */
  getExperienceById: async (id) => {
    try {
      const experience = await experienceRepository.findById(id);

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceService;
