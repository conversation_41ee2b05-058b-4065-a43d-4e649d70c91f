/**
 * User Focus Module
 *
 * This module handles Focus-related functionality for regular users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const userFocusController = require('./focus.controller');
const focusValidation = require('@admin/focus/focus.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all focus areas
  router.get(
    '/',
    authenticate,
    validate(focusValidation.getAll),
    userFocusController.getAllFocusAreas
  );

  // Get a focus area by ID
  router.get(
    '/:id',
    authenticate,
    validate(focusValidation.getById),
    userFocusController.getFocusById
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
