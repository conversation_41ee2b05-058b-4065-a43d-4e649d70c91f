openapi: 3.0.0
info:
  title: WTD Platform User Authentication API
  version: 1.0.0
  description: Authentication endpoints for WTD Platform users

paths:
  # /user/register:
  #   post:
  #     tags:
  #       - User
  #     summary: User Registration
  #     description: Register a new user
  #     requestBody:
  #       required: true
  #       content:
  #         application/json:
  #           schema:
  #             $ref: '#/components/schemas/UserRegisterRequest'
  #     responses:
  #       '201':
  #         description: Successfully registered
  #         content:
  #           application/json:
  #             schema:
  #               $ref: '#/components/schemas/AuthResponse'
  #       '422':
  #         description: Validation error
  #       '409':
  #         description: Email already exists

  /user/login:
    post:
      tags:
        - User
      summary: User Login
      description: Authenticate a user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
      responses:
        '200':
          description: Successfully authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
        '422':
          description: Validation error

  /user/profile:
    get:
      tags:
        - User
      summary: Get User Profile
      description: Get current user's profile details
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
        '401':
          description: Unauthorized

    # put:
    #   tags:
    #     - User
    #   summary: Update Profile
    #   description: Update current user's profile
    #   security:
    #     - BearerAuth: []
    #   requestBody:
    #     required: true
    #     content:
    #       application/json:
    #         schema:
    #           $ref: '#/components/schemas/UpdateProfileRequest'
    #   responses:
    #     '200':
    #       description: Profile updated successfully
    #       content:
    #         application/json:
    #           schema:
    #             $ref: '#/components/schemas/ProfileResponse'
    #     '400':
    #       description: Invalid input
    #     '401':
    #       description: Unauthorized

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    UserRegisterRequest:
      type: object
      required:
        - email
        - password
        - firstName
        - lastName
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          format: password
        firstName:
          type: string
        lastName:
          type: string

    UserLoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          format: password

    AuthResponse:
      type: object
      properties:
        token:
          type: string
        user:
          $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: integer
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        role:
          type: string
          enum: [user]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ProfileResponse:
      type: object
      properties:
        status:
          type: string
        message:
          type: string
        data:
          $ref: '#/components/schemas/UserProfile'

    UserProfile:
      type: object
      properties:
        id:
          type: integer
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        avatar:
          type: string
          format: uri
        profilePic:
          type: string
          format: uri
          nullable: true
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateProfileRequest:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        avatar:
          type: string
          format: uri
        profilePic:
          type: string
          format: uri